# Modern Portfolio Website - <PERSON><PERSON>

A modern, responsive portfolio website showcasing professional experience, skills, and projects. Built with modern web technologies and featuring a sleek, professional design with glassmorphism effects and interactive elements.

![Portfolio Preview](assets/img/my-profile-img.jpg)

## 🌟 Features

### Modern UI Components
- Glassmorphism effects across various sections
- Floating animations for enhanced visual appeal
- Neon glow effects on interactive elements
- Responsive design that works on all devices
- Smooth scrolling and transitions
- Dark theme optimized for better readability

### Key Sections
1. **Hero Section**
   - Dynamic typing animation
   - Full-screen background with modern overlay
   - Prominent name display with bold typography

2. **About**
   - Professional summary
   - Personal information
   - Contact details

3. **Skills**
   - Modern card-based layout
   - Tag-style skill representation
   - Two main categories:
     - Programming Languages & Frameworks
     - Design & Prototyping
   - Interactive hover effects

4. **Resume**
   - Educational background (BCA)
   - Professional experience
   - Social Media Ambassador role
   - Freelance work history

5. **Portfolio**
   - Filterable project gallery
   - Interactive project cards
   - Lightbox image viewing
   - Project details modal

6. **Services**
   - Service cards with icons
   - Detailed service descriptions
   - Interactive hover effects

7. **Contact**
   - Contact form with validation
   - Google Maps integration
   - Social media links
   - Direct contact information

## 🛠 Technologies Used

### Frontend
- HTML5
- CSS3 with modern features
- JavaScript (ES6+)
- Bootstrap 5.3.3

### Libraries & Frameworks
- AOS (Animate On Scroll)
- Typed.js (Text animation)
- Isotope (Portfolio filtering)
- GLightbox (Image lightbox)
- Swiper (Testimonials carousel)
- Waypoints (Scroll triggers)
- PureCounter (Statistics animation)

### Icons & Fonts
- Bootstrap Icons
- Google Fonts:
  - Roboto
  - Poppins
  - Raleway

## 📦 Project Structure

```
iPortfolio/
├── index.html                 # Main website file
├── portfolio-details.html     # Portfolio item details
├── service-details.html       # Service details
├── assets/
│   ├── css/
│   │   └── main.css          # Main stylesheet
│   ├── js/
│   │   └── main.js           # Main JavaScript file
│   ├── img/                   # Image assets
│   │   ├── portfolio/        # Portfolio images
│   │   └── testimonials/     # Testimonial images
│   ├── scss/                 # SCSS source files
│   └── vendor/               # Third-party libraries
│       ├── aos/              # Animate On Scroll
│       ├── bootstrap/        # Bootstrap framework
│       ├── bootstrap-icons/  # Bootstrap icons
│       ├── glightbox/        # Lightbox library
│       ├── isotope-layout/   # Portfolio filtering
│       ├── swiper/           # Testimonials slider
│       ├── typed.js/         # Typing animation
│       └── waypoints/        # Scrolling animations
└── forms/
    └── contact.php           # Contact form handler

```

## ⚙️ Setup & Installation

1. **Clone the repository**
   ```bash
   git clone [repository-url]
   ```

2. **Navigate to project directory**
   ```bash
   cd iPortfolio
   ```

3. **Open in browser**
   - Open `index.html` in a modern web browser
   - Or use a local development server:
     ```bash
     # Using Python
     python -m http.server 8000
     
     # Using Node.js
     npx serve
     ```

## 🎨 Customization

### Colors
The website uses a carefully chosen color palette that can be customized in `assets/css/main.css`:
- Primary: Custom dark theme with glassmorphism
- Accent: Neon effects for highlights
- Background: Dark gradients with overlay effects

### Content
1. Update `index.html` with your personal information
2. Replace images in `assets/img/` with your own
3. Modify portfolio items in the Portfolio section
4. Update resume information in the Resume section
5. Customize services in the Services section

### Styling
1. Main styles are in `assets/css/main.css`
2. Custom animations can be added in the CSS file
3. Bootstrap classes can be modified for layout changes

## 🚀 Performance Optimizations

- Optimized image loading with lazy loading
- Minified CSS and JavaScript files
- Efficient animation handling with AOS
- Browser compatibility fixes
- Mobile-first responsive design

## 🔐 Browser Compatibility

Tested and working on:
- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
- Mobile browsers (iOS/Android)

## 👥 Credits

- Design Template: iPortfolio by BootstrapMade
- Icons: Bootstrap Icons
- Images: Custom and portfolio work
- Animations: AOS Library

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

---

## 🤝 Contact

Anish Gupta
- Email: <EMAIL>
- LinkedIn: [Anish Gupta](https://www.linkedin.com/in/anish-gupta-696245324)
- GitHub: [anishgupta6801](https://github.com/anishgupta6801)

---

*Last Updated: June 21, 2025*
