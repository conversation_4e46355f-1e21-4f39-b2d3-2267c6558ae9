{"version": 3, "file": "aos.js", "sources": ["../node_modules/lodash.throttle/index.js", "../node_modules/lodash.debounce/index.js", "../src/js/libs/observer.js", "../src/js/helpers/detector.js", "../src/js/helpers/handleScroll.js", "../src/js/libs/offset.js", "../src/js/helpers/getInlineOption.js", "../src/js/helpers/prepare.js", "../src/js/helpers/offsetCalculator.js", "../src/js/helpers/elements.js", "../src/js/aos.js"], "sourcesContent": ["/**\n * lodash (Custom Build) <https://lodash.com/>\n * Build: `lodash modularize exports=\"npm\" -o ./`\n * Copyright jQuery Foundation and other contributors <https://jquery.org/>\n * Released under MIT license <https://lodash.com/license>\n * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>\n * Copyright <PERSON>, DocumentCloud and Investigative Reporters & Editors\n */\n\n/** Used as the `TypeError` message for \"Functions\" methods. */\nvar FUNC_ERROR_TEXT = 'Expected a function';\n\n/** Used as references for various `Number` constants. */\nvar NAN = 0 / 0;\n\n/** `Object#toString` result references. */\nvar symbolTag = '[object Symbol]';\n\n/** Used to match leading and trailing whitespace. */\nvar reTrim = /^\\s+|\\s+$/g;\n\n/** Used to detect bad signed hexadecimal string values. */\nvar reIsBadHex = /^[-+]0x[0-9a-f]+$/i;\n\n/** Used to detect binary string values. */\nvar reIsBinary = /^0b[01]+$/i;\n\n/** Used to detect octal string values. */\nvar reIsOctal = /^0o[0-7]+$/i;\n\n/** Built-in method references without a dependency on `root`. */\nvar freeParseInt = parseInt;\n\n/** Detect free variable `global` from Node.js. */\nvar freeGlobal = typeof global == 'object' && global && global.Object === Object && global;\n\n/** Detect free variable `self`. */\nvar freeSelf = typeof self == 'object' && self && self.Object === Object && self;\n\n/** Used as a reference to the global object. */\nvar root = freeGlobal || freeSelf || Function('return this')();\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/**\n * Used to resolve the\n * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)\n * of values.\n */\nvar objectToString = objectProto.toString;\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeMax = Math.max,\n    nativeMin = Math.min;\n\n/**\n * Gets the timestamp of the number of milliseconds that have elapsed since\n * the Unix epoch (1 January 1970 00:00:00 UTC).\n *\n * @static\n * @memberOf _\n * @since 2.4.0\n * @category Date\n * @returns {number} Returns the timestamp.\n * @example\n *\n * _.defer(function(stamp) {\n *   console.log(_.now() - stamp);\n * }, _.now());\n * // => Logs the number of milliseconds it took for the deferred invocation.\n */\nvar now = function() {\n  return root.Date.now();\n};\n\n/**\n * Creates a debounced function that delays invoking `func` until after `wait`\n * milliseconds have elapsed since the last time the debounced function was\n * invoked. The debounced function comes with a `cancel` method to cancel\n * delayed `func` invocations and a `flush` method to immediately invoke them.\n * Provide `options` to indicate whether `func` should be invoked on the\n * leading and/or trailing edge of the `wait` timeout. The `func` is invoked\n * with the last arguments provided to the debounced function. Subsequent\n * calls to the debounced function return the result of the last `func`\n * invocation.\n *\n * **Note:** If `leading` and `trailing` options are `true`, `func` is\n * invoked on the trailing edge of the timeout only if the debounced function\n * is invoked more than once during the `wait` timeout.\n *\n * If `wait` is `0` and `leading` is `false`, `func` invocation is deferred\n * until to the next tick, similar to `setTimeout` with a timeout of `0`.\n *\n * See [David Corbacho's article](https://css-tricks.com/debouncing-throttling-explained-examples/)\n * for details over the differences between `_.debounce` and `_.throttle`.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Function\n * @param {Function} func The function to debounce.\n * @param {number} [wait=0] The number of milliseconds to delay.\n * @param {Object} [options={}] The options object.\n * @param {boolean} [options.leading=false]\n *  Specify invoking on the leading edge of the timeout.\n * @param {number} [options.maxWait]\n *  The maximum time `func` is allowed to be delayed before it's invoked.\n * @param {boolean} [options.trailing=true]\n *  Specify invoking on the trailing edge of the timeout.\n * @returns {Function} Returns the new debounced function.\n * @example\n *\n * // Avoid costly calculations while the window size is in flux.\n * jQuery(window).on('resize', _.debounce(calculateLayout, 150));\n *\n * // Invoke `sendMail` when clicked, debouncing subsequent calls.\n * jQuery(element).on('click', _.debounce(sendMail, 300, {\n *   'leading': true,\n *   'trailing': false\n * }));\n *\n * // Ensure `batchLog` is invoked once after 1 second of debounced calls.\n * var debounced = _.debounce(batchLog, 250, { 'maxWait': 1000 });\n * var source = new EventSource('/stream');\n * jQuery(source).on('message', debounced);\n *\n * // Cancel the trailing debounced invocation.\n * jQuery(window).on('popstate', debounced.cancel);\n */\nfunction debounce(func, wait, options) {\n  var lastArgs,\n      lastThis,\n      maxWait,\n      result,\n      timerId,\n      lastCallTime,\n      lastInvokeTime = 0,\n      leading = false,\n      maxing = false,\n      trailing = true;\n\n  if (typeof func != 'function') {\n    throw new TypeError(FUNC_ERROR_TEXT);\n  }\n  wait = toNumber(wait) || 0;\n  if (isObject(options)) {\n    leading = !!options.leading;\n    maxing = 'maxWait' in options;\n    maxWait = maxing ? nativeMax(toNumber(options.maxWait) || 0, wait) : maxWait;\n    trailing = 'trailing' in options ? !!options.trailing : trailing;\n  }\n\n  function invokeFunc(time) {\n    var args = lastArgs,\n        thisArg = lastThis;\n\n    lastArgs = lastThis = undefined;\n    lastInvokeTime = time;\n    result = func.apply(thisArg, args);\n    return result;\n  }\n\n  function leadingEdge(time) {\n    // Reset any `maxWait` timer.\n    lastInvokeTime = time;\n    // Start the timer for the trailing edge.\n    timerId = setTimeout(timerExpired, wait);\n    // Invoke the leading edge.\n    return leading ? invokeFunc(time) : result;\n  }\n\n  function remainingWait(time) {\n    var timeSinceLastCall = time - lastCallTime,\n        timeSinceLastInvoke = time - lastInvokeTime,\n        result = wait - timeSinceLastCall;\n\n    return maxing ? nativeMin(result, maxWait - timeSinceLastInvoke) : result;\n  }\n\n  function shouldInvoke(time) {\n    var timeSinceLastCall = time - lastCallTime,\n        timeSinceLastInvoke = time - lastInvokeTime;\n\n    // Either this is the first call, activity has stopped and we're at the\n    // trailing edge, the system time has gone backwards and we're treating\n    // it as the trailing edge, or we've hit the `maxWait` limit.\n    return (lastCallTime === undefined || (timeSinceLastCall >= wait) ||\n      (timeSinceLastCall < 0) || (maxing && timeSinceLastInvoke >= maxWait));\n  }\n\n  function timerExpired() {\n    var time = now();\n    if (shouldInvoke(time)) {\n      return trailingEdge(time);\n    }\n    // Restart the timer.\n    timerId = setTimeout(timerExpired, remainingWait(time));\n  }\n\n  function trailingEdge(time) {\n    timerId = undefined;\n\n    // Only invoke if we have `lastArgs` which means `func` has been\n    // debounced at least once.\n    if (trailing && lastArgs) {\n      return invokeFunc(time);\n    }\n    lastArgs = lastThis = undefined;\n    return result;\n  }\n\n  function cancel() {\n    if (timerId !== undefined) {\n      clearTimeout(timerId);\n    }\n    lastInvokeTime = 0;\n    lastArgs = lastCallTime = lastThis = timerId = undefined;\n  }\n\n  function flush() {\n    return timerId === undefined ? result : trailingEdge(now());\n  }\n\n  function debounced() {\n    var time = now(),\n        isInvoking = shouldInvoke(time);\n\n    lastArgs = arguments;\n    lastThis = this;\n    lastCallTime = time;\n\n    if (isInvoking) {\n      if (timerId === undefined) {\n        return leadingEdge(lastCallTime);\n      }\n      if (maxing) {\n        // Handle invocations in a tight loop.\n        timerId = setTimeout(timerExpired, wait);\n        return invokeFunc(lastCallTime);\n      }\n    }\n    if (timerId === undefined) {\n      timerId = setTimeout(timerExpired, wait);\n    }\n    return result;\n  }\n  debounced.cancel = cancel;\n  debounced.flush = flush;\n  return debounced;\n}\n\n/**\n * Creates a throttled function that only invokes `func` at most once per\n * every `wait` milliseconds. The throttled function comes with a `cancel`\n * method to cancel delayed `func` invocations and a `flush` method to\n * immediately invoke them. Provide `options` to indicate whether `func`\n * should be invoked on the leading and/or trailing edge of the `wait`\n * timeout. The `func` is invoked with the last arguments provided to the\n * throttled function. Subsequent calls to the throttled function return the\n * result of the last `func` invocation.\n *\n * **Note:** If `leading` and `trailing` options are `true`, `func` is\n * invoked on the trailing edge of the timeout only if the throttled function\n * is invoked more than once during the `wait` timeout.\n *\n * If `wait` is `0` and `leading` is `false`, `func` invocation is deferred\n * until to the next tick, similar to `setTimeout` with a timeout of `0`.\n *\n * See [David Corbacho's article](https://css-tricks.com/debouncing-throttling-explained-examples/)\n * for details over the differences between `_.throttle` and `_.debounce`.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Function\n * @param {Function} func The function to throttle.\n * @param {number} [wait=0] The number of milliseconds to throttle invocations to.\n * @param {Object} [options={}] The options object.\n * @param {boolean} [options.leading=true]\n *  Specify invoking on the leading edge of the timeout.\n * @param {boolean} [options.trailing=true]\n *  Specify invoking on the trailing edge of the timeout.\n * @returns {Function} Returns the new throttled function.\n * @example\n *\n * // Avoid excessively updating the position while scrolling.\n * jQuery(window).on('scroll', _.throttle(updatePosition, 100));\n *\n * // Invoke `renewToken` when the click event is fired, but not more than once every 5 minutes.\n * var throttled = _.throttle(renewToken, 300000, { 'trailing': false });\n * jQuery(element).on('click', throttled);\n *\n * // Cancel the trailing throttled invocation.\n * jQuery(window).on('popstate', throttled.cancel);\n */\nfunction throttle(func, wait, options) {\n  var leading = true,\n      trailing = true;\n\n  if (typeof func != 'function') {\n    throw new TypeError(FUNC_ERROR_TEXT);\n  }\n  if (isObject(options)) {\n    leading = 'leading' in options ? !!options.leading : leading;\n    trailing = 'trailing' in options ? !!options.trailing : trailing;\n  }\n  return debounce(func, wait, {\n    'leading': leading,\n    'maxWait': wait,\n    'trailing': trailing\n  });\n}\n\n/**\n * Checks if `value` is the\n * [language type](http://www.ecma-international.org/ecma-262/7.0/#sec-ecmascript-language-types)\n * of `Object`. (e.g. arrays, functions, objects, regexes, `new Number(0)`, and `new String('')`)\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an object, else `false`.\n * @example\n *\n * _.isObject({});\n * // => true\n *\n * _.isObject([1, 2, 3]);\n * // => true\n *\n * _.isObject(_.noop);\n * // => true\n *\n * _.isObject(null);\n * // => false\n */\nfunction isObject(value) {\n  var type = typeof value;\n  return !!value && (type == 'object' || type == 'function');\n}\n\n/**\n * Checks if `value` is object-like. A value is object-like if it's not `null`\n * and has a `typeof` result of \"object\".\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is object-like, else `false`.\n * @example\n *\n * _.isObjectLike({});\n * // => true\n *\n * _.isObjectLike([1, 2, 3]);\n * // => true\n *\n * _.isObjectLike(_.noop);\n * // => false\n *\n * _.isObjectLike(null);\n * // => false\n */\nfunction isObjectLike(value) {\n  return !!value && typeof value == 'object';\n}\n\n/**\n * Checks if `value` is classified as a `Symbol` primitive or object.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a symbol, else `false`.\n * @example\n *\n * _.isSymbol(Symbol.iterator);\n * // => true\n *\n * _.isSymbol('abc');\n * // => false\n */\nfunction isSymbol(value) {\n  return typeof value == 'symbol' ||\n    (isObjectLike(value) && objectToString.call(value) == symbolTag);\n}\n\n/**\n * Converts `value` to a number.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to process.\n * @returns {number} Returns the number.\n * @example\n *\n * _.toNumber(3.2);\n * // => 3.2\n *\n * _.toNumber(Number.MIN_VALUE);\n * // => 5e-324\n *\n * _.toNumber(Infinity);\n * // => Infinity\n *\n * _.toNumber('3.2');\n * // => 3.2\n */\nfunction toNumber(value) {\n  if (typeof value == 'number') {\n    return value;\n  }\n  if (isSymbol(value)) {\n    return NAN;\n  }\n  if (isObject(value)) {\n    var other = typeof value.valueOf == 'function' ? value.valueOf() : value;\n    value = isObject(other) ? (other + '') : other;\n  }\n  if (typeof value != 'string') {\n    return value === 0 ? value : +value;\n  }\n  value = value.replace(reTrim, '');\n  var isBinary = reIsBinary.test(value);\n  return (isBinary || reIsOctal.test(value))\n    ? freeParseInt(value.slice(2), isBinary ? 2 : 8)\n    : (reIsBadHex.test(value) ? NAN : +value);\n}\n\nmodule.exports = throttle;\n", "/**\n * lodash (Custom Build) <https://lodash.com/>\n * Build: `lodash modularize exports=\"npm\" -o ./`\n * Copyright jQuery Foundation and other contributors <https://jquery.org/>\n * Released under MIT license <https://lodash.com/license>\n * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>\n * Copyright <PERSON>, DocumentCloud and Investigative Reporters & Editors\n */\n\n/** Used as the `TypeError` message for \"Functions\" methods. */\nvar FUNC_ERROR_TEXT = 'Expected a function';\n\n/** Used as references for various `Number` constants. */\nvar NAN = 0 / 0;\n\n/** `Object#toString` result references. */\nvar symbolTag = '[object Symbol]';\n\n/** Used to match leading and trailing whitespace. */\nvar reTrim = /^\\s+|\\s+$/g;\n\n/** Used to detect bad signed hexadecimal string values. */\nvar reIsBadHex = /^[-+]0x[0-9a-f]+$/i;\n\n/** Used to detect binary string values. */\nvar reIsBinary = /^0b[01]+$/i;\n\n/** Used to detect octal string values. */\nvar reIsOctal = /^0o[0-7]+$/i;\n\n/** Built-in method references without a dependency on `root`. */\nvar freeParseInt = parseInt;\n\n/** Detect free variable `global` from Node.js. */\nvar freeGlobal = typeof global == 'object' && global && global.Object === Object && global;\n\n/** Detect free variable `self`. */\nvar freeSelf = typeof self == 'object' && self && self.Object === Object && self;\n\n/** Used as a reference to the global object. */\nvar root = freeGlobal || freeSelf || Function('return this')();\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/**\n * Used to resolve the\n * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)\n * of values.\n */\nvar objectToString = objectProto.toString;\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeMax = Math.max,\n    nativeMin = Math.min;\n\n/**\n * Gets the timestamp of the number of milliseconds that have elapsed since\n * the Unix epoch (1 January 1970 00:00:00 UTC).\n *\n * @static\n * @memberOf _\n * @since 2.4.0\n * @category Date\n * @returns {number} Returns the timestamp.\n * @example\n *\n * _.defer(function(stamp) {\n *   console.log(_.now() - stamp);\n * }, _.now());\n * // => Logs the number of milliseconds it took for the deferred invocation.\n */\nvar now = function() {\n  return root.Date.now();\n};\n\n/**\n * Creates a debounced function that delays invoking `func` until after `wait`\n * milliseconds have elapsed since the last time the debounced function was\n * invoked. The debounced function comes with a `cancel` method to cancel\n * delayed `func` invocations and a `flush` method to immediately invoke them.\n * Provide `options` to indicate whether `func` should be invoked on the\n * leading and/or trailing edge of the `wait` timeout. The `func` is invoked\n * with the last arguments provided to the debounced function. Subsequent\n * calls to the debounced function return the result of the last `func`\n * invocation.\n *\n * **Note:** If `leading` and `trailing` options are `true`, `func` is\n * invoked on the trailing edge of the timeout only if the debounced function\n * is invoked more than once during the `wait` timeout.\n *\n * If `wait` is `0` and `leading` is `false`, `func` invocation is deferred\n * until to the next tick, similar to `setTimeout` with a timeout of `0`.\n *\n * See [David Corbacho's article](https://css-tricks.com/debouncing-throttling-explained-examples/)\n * for details over the differences between `_.debounce` and `_.throttle`.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Function\n * @param {Function} func The function to debounce.\n * @param {number} [wait=0] The number of milliseconds to delay.\n * @param {Object} [options={}] The options object.\n * @param {boolean} [options.leading=false]\n *  Specify invoking on the leading edge of the timeout.\n * @param {number} [options.maxWait]\n *  The maximum time `func` is allowed to be delayed before it's invoked.\n * @param {boolean} [options.trailing=true]\n *  Specify invoking on the trailing edge of the timeout.\n * @returns {Function} Returns the new debounced function.\n * @example\n *\n * // Avoid costly calculations while the window size is in flux.\n * jQuery(window).on('resize', _.debounce(calculateLayout, 150));\n *\n * // Invoke `sendMail` when clicked, debouncing subsequent calls.\n * jQuery(element).on('click', _.debounce(sendMail, 300, {\n *   'leading': true,\n *   'trailing': false\n * }));\n *\n * // Ensure `batchLog` is invoked once after 1 second of debounced calls.\n * var debounced = _.debounce(batchLog, 250, { 'maxWait': 1000 });\n * var source = new EventSource('/stream');\n * jQuery(source).on('message', debounced);\n *\n * // Cancel the trailing debounced invocation.\n * jQuery(window).on('popstate', debounced.cancel);\n */\nfunction debounce(func, wait, options) {\n  var lastArgs,\n      lastThis,\n      maxWait,\n      result,\n      timerId,\n      lastCallTime,\n      lastInvokeTime = 0,\n      leading = false,\n      maxing = false,\n      trailing = true;\n\n  if (typeof func != 'function') {\n    throw new TypeError(FUNC_ERROR_TEXT);\n  }\n  wait = toNumber(wait) || 0;\n  if (isObject(options)) {\n    leading = !!options.leading;\n    maxing = 'maxWait' in options;\n    maxWait = maxing ? nativeMax(toNumber(options.maxWait) || 0, wait) : maxWait;\n    trailing = 'trailing' in options ? !!options.trailing : trailing;\n  }\n\n  function invokeFunc(time) {\n    var args = lastArgs,\n        thisArg = lastThis;\n\n    lastArgs = lastThis = undefined;\n    lastInvokeTime = time;\n    result = func.apply(thisArg, args);\n    return result;\n  }\n\n  function leadingEdge(time) {\n    // Reset any `maxWait` timer.\n    lastInvokeTime = time;\n    // Start the timer for the trailing edge.\n    timerId = setTimeout(timerExpired, wait);\n    // Invoke the leading edge.\n    return leading ? invokeFunc(time) : result;\n  }\n\n  function remainingWait(time) {\n    var timeSinceLastCall = time - lastCallTime,\n        timeSinceLastInvoke = time - lastInvokeTime,\n        result = wait - timeSinceLastCall;\n\n    return maxing ? nativeMin(result, maxWait - timeSinceLastInvoke) : result;\n  }\n\n  function shouldInvoke(time) {\n    var timeSinceLastCall = time - lastCallTime,\n        timeSinceLastInvoke = time - lastInvokeTime;\n\n    // Either this is the first call, activity has stopped and we're at the\n    // trailing edge, the system time has gone backwards and we're treating\n    // it as the trailing edge, or we've hit the `maxWait` limit.\n    return (lastCallTime === undefined || (timeSinceLastCall >= wait) ||\n      (timeSinceLastCall < 0) || (maxing && timeSinceLastInvoke >= maxWait));\n  }\n\n  function timerExpired() {\n    var time = now();\n    if (shouldInvoke(time)) {\n      return trailingEdge(time);\n    }\n    // Restart the timer.\n    timerId = setTimeout(timerExpired, remainingWait(time));\n  }\n\n  function trailingEdge(time) {\n    timerId = undefined;\n\n    // Only invoke if we have `lastArgs` which means `func` has been\n    // debounced at least once.\n    if (trailing && lastArgs) {\n      return invokeFunc(time);\n    }\n    lastArgs = lastThis = undefined;\n    return result;\n  }\n\n  function cancel() {\n    if (timerId !== undefined) {\n      clearTimeout(timerId);\n    }\n    lastInvokeTime = 0;\n    lastArgs = lastCallTime = lastThis = timerId = undefined;\n  }\n\n  function flush() {\n    return timerId === undefined ? result : trailingEdge(now());\n  }\n\n  function debounced() {\n    var time = now(),\n        isInvoking = shouldInvoke(time);\n\n    lastArgs = arguments;\n    lastThis = this;\n    lastCallTime = time;\n\n    if (isInvoking) {\n      if (timerId === undefined) {\n        return leadingEdge(lastCallTime);\n      }\n      if (maxing) {\n        // Handle invocations in a tight loop.\n        timerId = setTimeout(timerExpired, wait);\n        return invokeFunc(lastCallTime);\n      }\n    }\n    if (timerId === undefined) {\n      timerId = setTimeout(timerExpired, wait);\n    }\n    return result;\n  }\n  debounced.cancel = cancel;\n  debounced.flush = flush;\n  return debounced;\n}\n\n/**\n * Checks if `value` is the\n * [language type](http://www.ecma-international.org/ecma-262/7.0/#sec-ecmascript-language-types)\n * of `Object`. (e.g. arrays, functions, objects, regexes, `new Number(0)`, and `new String('')`)\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an object, else `false`.\n * @example\n *\n * _.isObject({});\n * // => true\n *\n * _.isObject([1, 2, 3]);\n * // => true\n *\n * _.isObject(_.noop);\n * // => true\n *\n * _.isObject(null);\n * // => false\n */\nfunction isObject(value) {\n  var type = typeof value;\n  return !!value && (type == 'object' || type == 'function');\n}\n\n/**\n * Checks if `value` is object-like. A value is object-like if it's not `null`\n * and has a `typeof` result of \"object\".\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is object-like, else `false`.\n * @example\n *\n * _.isObjectLike({});\n * // => true\n *\n * _.isObjectLike([1, 2, 3]);\n * // => true\n *\n * _.isObjectLike(_.noop);\n * // => false\n *\n * _.isObjectLike(null);\n * // => false\n */\nfunction isObjectLike(value) {\n  return !!value && typeof value == 'object';\n}\n\n/**\n * Checks if `value` is classified as a `Symbol` primitive or object.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a symbol, else `false`.\n * @example\n *\n * _.isSymbol(Symbol.iterator);\n * // => true\n *\n * _.isSymbol('abc');\n * // => false\n */\nfunction isSymbol(value) {\n  return typeof value == 'symbol' ||\n    (isObjectLike(value) && objectToString.call(value) == symbolTag);\n}\n\n/**\n * Converts `value` to a number.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to process.\n * @returns {number} Returns the number.\n * @example\n *\n * _.toNumber(3.2);\n * // => 3.2\n *\n * _.toNumber(Number.MIN_VALUE);\n * // => 5e-324\n *\n * _.toNumber(Infinity);\n * // => Infinity\n *\n * _.toNumber('3.2');\n * // => 3.2\n */\nfunction toNumber(value) {\n  if (typeof value == 'number') {\n    return value;\n  }\n  if (isSymbol(value)) {\n    return NAN;\n  }\n  if (isObject(value)) {\n    var other = typeof value.valueOf == 'function' ? value.valueOf() : value;\n    value = isObject(other) ? (other + '') : other;\n  }\n  if (typeof value != 'string') {\n    return value === 0 ? value : +value;\n  }\n  value = value.replace(reTrim, '');\n  var isBinary = reIsBinary.test(value);\n  return (isBinary || reIsOctal.test(value))\n    ? freeParseInt(value.slice(2), isBinary ? 2 : 8)\n    : (reIsBadHex.test(value) ? NAN : +value);\n}\n\nmodule.exports = debounce;\n", "let callback = () => {};\n\nfunction containsAOSNode(nodes) {\n  let i, currentNode, result;\n\n  for (i = 0; i < nodes.length; i += 1) {\n    currentNode = nodes[i];\n\n    if (currentNode.dataset && currentNode.dataset.aos) {\n      return true;\n    }\n\n    result = currentNode.children && containsAOSNode(currentNode.children);\n\n    if (result) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\nfunction check(mutations) {\n  if (!mutations) return;\n\n  mutations.forEach(mutation => {\n    const addedNodes = Array.prototype.slice.call(mutation.addedNodes);\n    const removedNodes = Array.prototype.slice.call(mutation.removedNodes);\n    const allNodes = addedNodes.concat(removedNodes);\n\n    if (containsAOSNode(allNodes)) {\n      return callback();\n    }\n  });\n}\n\nfunction getMutationObserver() {\n  return (\n    window.MutationObserver ||\n    window.WebKitMutationObserver ||\n    window.MozMutationObserver\n  );\n}\n\nfunction isSupported() {\n  return !!getMutationObserver();\n}\n\nfunction ready(selector, fn) {\n  const doc = window.document;\n  const MutationObserver = getMutationObserver();\n\n  const observer = new MutationObserver(check);\n  callback = fn;\n\n  observer.observe(doc.documentElement, {\n    childList: true,\n    subtree: true,\n    removedNodes: true\n  });\n}\n\nexport default { isSupported, ready };\n", "/**\n * Device detector\n */\n\nconst fullNameRe = /(android|bb\\d+|meego).+mobile|avantgo|bada\\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i;\nconst prefixRe = /1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\\-(n|u)|c55\\/|capi|ccwa|cdm\\-|cell|chtm|cldc|cmd\\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\\-s|devi|dica|dmob|do(c|p)o|ds(12|\\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\\-|_)|g1 u|g560|gene|gf\\-5|g\\-mo|go(\\.w|od)|gr(ad|un)|haie|hcit|hd\\-(m|p|t)|hei\\-|hi(pt|ta)|hp( i|ip)|hs\\-c|ht(c(\\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\\-(20|go|ma)|i230|iac( |\\-|\\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\\/)|klon|kpt |kwc\\-|kyo(c|k)|le(no|xi)|lg( g|\\/(k|l|u)|50|54|\\-[a-w])|libw|lynx|m1\\-w|m3ga|m50\\/|ma(te|ui|xo)|mc(01|21|ca)|m\\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\\-2|po(ck|rt|se)|prox|psio|pt\\-g|qa\\-a|qc(07|12|21|32|60|\\-[2-7]|i\\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\\-|oo|p\\-)|sdk\\/|se(c(\\-|0|1)|47|mc|nd|ri)|sgh\\-|shar|sie(\\-|m)|sk\\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\\-|v\\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\\-|tdg\\-|tel(i|m)|tim\\-|t\\-mo|to(pl|sh)|ts(70|m\\-|m3|m5)|tx\\-9|up(\\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\\-|your|zeto|zte\\-/i;\nconst fullNameMobileRe = /(android|bb\\d+|meego).+mobile|avantgo|bada\\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\\.(browser|link)|vodafone|wap|windows ce|xda|xiino|android|ipad|playbook|silk/i;\nconst prefixMobileRe = /1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\\-(n|u)|c55\\/|capi|ccwa|cdm\\-|cell|chtm|cldc|cmd\\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\\-s|devi|dica|dmob|do(c|p)o|ds(12|\\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\\-|_)|g1 u|g560|gene|gf\\-5|g\\-mo|go(\\.w|od)|gr(ad|un)|haie|hcit|hd\\-(m|p|t)|hei\\-|hi(pt|ta)|hp( i|ip)|hs\\-c|ht(c(\\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\\-(20|go|ma)|i230|iac( |\\-|\\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\\/)|klon|kpt |kwc\\-|kyo(c|k)|le(no|xi)|lg( g|\\/(k|l|u)|50|54|\\-[a-w])|libw|lynx|m1\\-w|m3ga|m50\\/|ma(te|ui|xo)|mc(01|21|ca)|m\\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\\-2|po(ck|rt|se)|prox|psio|pt\\-g|qa\\-a|qc(07|12|21|32|60|\\-[2-7]|i\\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\\-|oo|p\\-)|sdk\\/|se(c(\\-|0|1)|47|mc|nd|ri)|sgh\\-|shar|sie(\\-|m)|sk\\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\\-|v\\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\\-|tdg\\-|tel(i|m)|tim\\-|t\\-mo|to(pl|sh)|ts(70|m\\-|m3|m5)|tx\\-9|up(\\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\\-|your|zeto|zte\\-/i;\n\nfunction ua() {\n  return navigator.userAgent || navigator.vendor || window.opera || '';\n}\n\nclass Detector {\n  phone() {\n    const a = ua();\n    return !!(fullNameRe.test(a) || prefixRe.test(a.substr(0, 4)));\n  }\n\n  mobile() {\n    const a = ua();\n    return !!(fullNameMobileRe.test(a) || prefixMobileRe.test(a.substr(0, 4)));\n  }\n\n  tablet() {\n    return this.mobile() && !this.phone();\n  }\n\n  // http://browserhacks.com/#hack-acea075d0ac6954f275a70023906050c\n  ie11() {\n    return (\n      '-ms-scroll-limit' in document.documentElement.style &&\n      '-ms-ime-align' in document.documentElement.style\n    );\n  }\n}\n\nexport default new Detector();\n", "import detect from './detector';\n\n/**\n * Adds multiple classes on node\n * @param {DOMNode} node\n * @param {array}  classes\n */\nconst addClasses = (node, classes) =>\n  classes && classes.forEach(className => node.classList.add(className));\n\n/**\n * Removes multiple classes from node\n * @param {DOMNode} node\n * @param {array}  classes\n */\nconst removeClasses = (node, classes) =>\n  classes && classes.forEach(className => node.classList.remove(className));\n\nconst fireEvent = (eventName, data) => {\n  let customEvent;\n\n  if (detect.ie11()) {\n    customEvent = document.createEvent('CustomEvent');\n    customEvent.initCustomEvent(eventName, true, true, { detail: data });\n  } else {\n    customEvent = new CustomEvent(eventName, {\n      detail: data\n    });\n  }\n\n  return document.dispatchEvent(customEvent);\n};\n\n/**\n * Set or remove aos-animate class\n * @param {node} el         element\n * @param {int}  top        scrolled distance\n */\nconst applyClasses = (el, top) => {\n  const { options, position, node, data } = el;\n\n  const hide = () => {\n    if (!el.animated) return;\n\n    removeClasses(node, options.animatedClassNames);\n    fireEvent('aos:out', node);\n\n    if (el.options.id) {\n      fireEvent(`aos:in:${el.options.id}`, node);\n    }\n\n    el.animated = false;\n  };\n\n  const show = () => {\n    if (el.animated) return;\n\n    addClasses(node, options.animatedClassNames);\n\n    fireEvent('aos:in', node);\n    if (el.options.id) {\n      fireEvent(`aos:in:${el.options.id}`, node);\n    }\n\n    el.animated = true;\n  };\n\n  if (options.mirror && top >= position.out && !options.once) {\n    hide();\n  } else if (top >= position.in) {\n    show();\n  } else if (el.animated && !options.once) {\n    hide();\n  }\n};\n\n/**\n * Scroll logic - add or remove 'aos-animate' class on scroll\n *\n * @param  {array} $elements         array of elements nodes\n * @return {void}\n */\nconst handleScroll = $elements =>\n  $elements.forEach((el, i) => applyClasses(el, window.pageYOffset));\n\nexport default handleScroll;\n", "/**\n * Get offset of DOM element\n * like there were no transforms applied on it\n *\n * @param  {Node} el [DOM element]\n * @return {Object} [top and left offset]\n */\nconst offset = function(el) {\n  let _x = 0;\n  let _y = 0;\n\n  while (el && !isNaN(el.offsetLeft) && !isNaN(el.offsetTop)) {\n    _x += el.offsetLeft - (el.tagName != 'BODY' ? el.scrollLeft : 0);\n    _y += el.offsetTop - (el.tagName != 'BODY' ? el.scrollTop : 0);\n    el = el.offsetParent;\n  }\n\n  return {\n    top: _y,\n    left: _x\n  };\n};\n\nexport default offset;\n", "/**\n * Get inline option with a fallback.\n *\n * @param  {Node} el [Dom element]\n * @param  {String} key [Option key]\n * @param  {String} fallback [Default (fallback) value]\n * @return {Mixed} [Option set with inline attributes or fallback value if not set]\n */\n\nexport default (el, key, fallback) => {\n  const attr = el.getAttribute('data-aos-' + key);\n\n  if (typeof attr !== 'undefined') {\n    if (attr === 'true') {\n      return true;\n    } else if (attr === 'false') {\n      return false;\n    }\n  }\n\n  return attr || fallback;\n};\n", "/* Clearing variables */\n\nimport { getPositionIn, getPositionOut } from './offsetCalculator';\nimport getInlineOption from './getInlineOption';\n\nconst prepare = function($elements, options) {\n  $elements.forEach((el, i) => {\n    const mirror = getInlineOption(el.node, 'mirror', options.mirror);\n    const once = getInlineOption(el.node, 'once', options.once);\n    const id = getInlineOption(el.node, 'id');\n    const customClassNames =\n      options.useClassNames && el.node.getAttribute('data-aos');\n\n    const animatedClassNames = [options.animatedClassName]\n      .concat(customClassNames ? customClassNames.split(' ') : [])\n      .filter(className => typeof className === 'string');\n\n    if (options.initClassName) {\n      el.node.classList.add(options.initClassName);\n    }\n\n    el.position = {\n      in: getPositionIn(el.node, options.offset, options.anchorPlacement),\n      out: mirror && getPositionOut(el.node, options.offset)\n    };\n\n    el.options = {\n      once,\n      mirror,\n      animatedClassNames,\n      id\n    };\n  });\n\n  return $elements;\n};\n\nexport default prepare;\n", "/**\n * Calculate offset\n * basing on element's settings like:\n * - anchor\n * - offset\n *\n * @param  {Node} el [Dom element]\n * @return {Integer} [Final offset that will be used to trigger animation in good position]\n */\n\nimport getOffset from './../libs/offset';\nimport getInlineOption from './getInlineOption';\n\nexport const getPositionIn = (el, defaultOffset, defaultAnchorPlacement) => {\n  const windowHeight = window.innerHeight;\n  const anchor = getInlineOption(el, 'anchor');\n  const inlineAnchorPlacement = getInlineOption(el, 'anchor-placement');\n  const additionalOffset = Number(\n    getInlineOption(el, 'offset', inlineAnchorPlacement ? 0 : defaultOffset)\n  );\n  const anchorPlacement = inlineAnchorPlacement || defaultAnchorPlacement;\n  let finalEl = el;\n\n  if (anchor && document.querySelectorAll(anchor)) {\n    finalEl = document.querySelectorAll(anchor)[0];\n  }\n\n  let triggerPoint = getOffset(finalEl).top - windowHeight;\n\n  switch (anchorPlacement) {\n    case 'top-bottom':\n      // Default offset\n      break;\n    case 'center-bottom':\n      triggerPoint += finalEl.offsetHeight / 2;\n      break;\n    case 'bottom-bottom':\n      triggerPoint += finalEl.offsetHeight;\n      break;\n    case 'top-center':\n      triggerPoint += windowHeight / 2;\n      break;\n    case 'center-center':\n      triggerPoint += windowHeight / 2 + finalEl.offsetHeight / 2;\n      break;\n    case 'bottom-center':\n      triggerPoint += windowHeight / 2 + finalEl.offsetHeight;\n      break;\n    case 'top-top':\n      triggerPoint += windowHeight;\n      break;\n    case 'bottom-top':\n      triggerPoint += windowHeight + finalEl.offsetHeight;\n      break;\n    case 'center-top':\n      triggerPoint += windowHeight + finalEl.offsetHeight / 2;\n      break;\n  }\n\n  return triggerPoint + additionalOffset;\n};\n\nexport const getPositionOut = (el, defaultOffset) => {\n  const windowHeight = window.innerHeight;\n  const anchor = getInlineOption(el, 'anchor');\n  const additionalOffset = getInlineOption(el, 'offset', defaultOffset);\n  let finalEl = el;\n\n  if (anchor && document.querySelectorAll(anchor)) {\n    finalEl = document.querySelectorAll(anchor)[0];\n  }\n\n  const elementOffsetTop = getOffset(finalEl).top;\n\n  return elementOffsetTop + finalEl.offsetHeight - additionalOffset;\n};\n", "/**\n * Generate initial array with elements as objects\n * This array will be extended later with elements attributes values\n * like 'position'\n */\nexport default () => {\n  const elements = document.querySelectorAll('[data-aos]');\n  return Array.prototype.map.call(elements, node => ({ node }));\n};\n", "/**\n * *******************************************************\n * A<PERSON> (Animate on scroll) - wowjs alternative\n * made to animate elements on scroll in both directions\n * *******************************************************\n */\nimport styles from './../sass/aos.scss';\n\n// Modules & helpers\nimport throttle from 'lodash.throttle';\nimport debounce from 'lodash.debounce';\n\nimport observer from './libs/observer';\n\nimport detect from './helpers/detector';\nimport handleScroll from './helpers/handleScroll';\nimport prepare from './helpers/prepare';\nimport elements from './helpers/elements';\n\n/**\n * Private variables\n */\nlet $aosElements = [];\nlet initialized = false;\n\n/**\n * Default options\n */\nlet options = {\n  offset: 120,\n  delay: 0,\n  easing: 'ease',\n  duration: 400,\n  disable: false,\n  once: false,\n  mirror: false,\n  anchorPlacement: 'top-bottom',\n  startEvent: 'DOMContentLoaded',\n  animatedClassName: 'aos-animate',\n  initClassName: 'aos-init',\n  useClassNames: false,\n  disableMutationObserver: false,\n  throttleDelay: 99,\n  debounceDelay: 50\n};\n\n// Detect not supported browsers (<=IE9)\n// http://browserhacks.com/#hack-e71d8692f65334173fee715c222cb805\nconst isBrowserNotSupported = () => document.all && !window.atob;\n\nconst initializeScroll = function initializeScroll() {\n  // Extend elements objects in $aosElements with their positions\n  $aosElements = prepare($aosElements, options);\n  // Perform scroll event, to refresh view and show/hide elements\n  handleScroll($aosElements);\n\n  /**\n   * Handle scroll event to animate elements on scroll\n   */\n  window.addEventListener(\n    'scroll',\n    throttle(() => {\n      handleScroll($aosElements, options.once);\n    }, options.throttleDelay)\n  );\n\n  return $aosElements;\n};\n\n/**\n * Refresh AOS\n */\nconst refresh = function refresh(initialize = false) {\n  // Allow refresh only when it was first initialized on startEvent\n  if (initialize) initialized = true;\n  if (initialized) initializeScroll();\n};\n\n/**\n * Hard refresh\n * create array with new elements and trigger refresh\n */\nconst refreshHard = function refreshHard() {\n  $aosElements = elements();\n\n  if (isDisabled(options.disable) || isBrowserNotSupported()) {\n    return disable();\n  }\n\n  refresh();\n};\n\n/**\n * Disable AOS\n * Remove all attributes to reset applied styles\n */\nconst disable = function() {\n  $aosElements.forEach(function(el, i) {\n    el.node.removeAttribute('data-aos');\n    el.node.removeAttribute('data-aos-easing');\n    el.node.removeAttribute('data-aos-duration');\n    el.node.removeAttribute('data-aos-delay');\n\n    if (options.initClassName) {\n      el.node.classList.remove(options.initClassName);\n    }\n\n    if (options.animatedClassName) {\n      el.node.classList.remove(options.animatedClassName);\n    }\n  });\n};\n\n/**\n * Check if AOS should be disabled based on provided setting\n */\nconst isDisabled = function(optionDisable) {\n  return (\n    optionDisable === true ||\n    (optionDisable === 'mobile' && detect.mobile()) ||\n    (optionDisable === 'phone' && detect.phone()) ||\n    (optionDisable === 'tablet' && detect.tablet()) ||\n    (typeof optionDisable === 'function' && optionDisable() === true)\n  );\n};\n\n/**\n * Initializing AOS\n * - Create options merging defaults with user defined options\n * - Set attributes on <body> as global setting - css relies on it\n * - Attach preparing elements to options.startEvent,\n *   window resize and orientation change\n * - Attach function that handle scroll and everything connected to it\n *   to window scroll event and fire once document is ready to set initial state\n */\nconst init = function init(settings) {\n  options = Object.assign(options, settings);\n\n  // Create initial array with elements -> to be fullfilled later with prepare()\n  $aosElements = elements();\n\n  /**\n   * Disable mutation observing if not supported\n   */\n  if (!options.disableMutationObserver && !observer.isSupported()) {\n    console.info(`\n      aos: MutationObserver is not supported on this browser,\n      code mutations observing has been disabled.\n      You may have to call \"refreshHard()\" by yourself.\n    `);\n    options.disableMutationObserver = true;\n  }\n\n  /**\n   * Observe [aos] elements\n   * If something is loaded by AJAX\n   * it'll refresh plugin automatically\n   */\n  if (!options.disableMutationObserver) {\n    observer.ready('[data-aos]', refreshHard);\n  }\n\n  /**\n   * Don't init plugin if option `disable` is set\n   * or when browser is not supported\n   */\n  if (isDisabled(options.disable) || isBrowserNotSupported()) {\n    return disable();\n  }\n\n  /**\n   * Set global settings on body, based on options\n   * so CSS can use it\n   */\n  document\n    .querySelector('body')\n    .setAttribute('data-aos-easing', options.easing);\n\n  document\n    .querySelector('body')\n    .setAttribute('data-aos-duration', options.duration);\n\n  document.querySelector('body').setAttribute('data-aos-delay', options.delay);\n\n  /**\n   * Handle initializing\n   */\n  if (['DOMContentLoaded', 'load'].indexOf(options.startEvent) === -1) {\n    // Listen to options.startEvent and initialize AOS\n    document.addEventListener(options.startEvent, function() {\n      refresh(true);\n    });\n  } else {\n    window.addEventListener('load', function() {\n      refresh(true);\n    });\n  }\n\n  if (\n    options.startEvent === 'DOMContentLoaded' &&\n    ['complete', 'interactive'].indexOf(document.readyState) > -1\n  ) {\n    // Initialize AOS if default startEvent was already fired\n    refresh(true);\n  }\n\n  /**\n   * Refresh plugin on window resize or orientation change\n   */\n  window.addEventListener(\n    'resize',\n    debounce(refresh, options.debounceDelay, true)\n  );\n\n  window.addEventListener(\n    'orientationchange',\n    debounce(refresh, options.debounceDelay, true)\n  );\n\n  return $aosElements;\n};\n\n/**\n * Export Public API\n */\n\nexport default {\n  init,\n  refresh,\n  refreshHard\n};\n"], "names": ["FUNC_ERROR_TEXT", "NAN", "symbolTag", "reTrim", "reIsBadHex", "reIsBinary", "reIsOctal", "freeParseInt", "parseInt", "freeGlobal", "global", "Object", "freeSelf", "self", "root", "Function", "objectToString", "prototype", "toString", "nativeMax", "Math", "max", "nativeMin", "min", "now", "Date", "debounce", "func", "wait", "options", "lastArgs", "lastThis", "max<PERSON><PERSON>", "result", "timerId", "lastCallTime", "lastInvokeTime", "leading", "maxing", "trailing", "TypeError", "invokeFunc", "time", "args", "thisArg", "undefined", "apply", "shouldInvoke", "timeSinceLastCall", "timerExpired", "trailingEdge", "setTimeout", "remainingWait", "debounced", "isInvoking", "arguments", "this", "leading<PERSON>dge", "toNumber", "isObject", "cancel", "clearTimeout", "flush", "value", "type", "isObjectLike", "call", "isSymbol", "other", "valueOf", "replace", "isBinary", "test", "slice", "callback", "check", "mutations", "for<PERSON>ach", "addedNodes", "Array", "mutation", "removedNodes", "containsAOSNode", "nodes", "i", "currentNode", "length", "dataset", "aos", "children", "concat", "getMutationObserver", "window", "MutationObserver", "WebKitMutationObserver", "MozMutationObserver", "isSupported", "ready", "selector", "fn", "doc", "document", "observer", "observe", "documentElement", "fullNameRe", "prefixRe", "fullNameMobileRe", "prefixMobileRe", "ua", "navigator", "userAgent", "vendor", "opera", "a", "substr", "mobile", "phone", "style", "fireEvent", "eventName", "data", "customEvent", "detect", "ie11", "createEvent", "initCustomEvent", "detail", "CustomEvent", "dispatchEvent", "handleScroll", "$elements", "el", "top", "position", "node", "hide", "animated", "classes", "classList", "remove", "className", "animatedClassNames", "id", "mirror", "out", "once", "in", "add", "applyClasses", "pageYOffset", "offset", "_x", "_y", "isNaN", "offsetLeft", "offsetTop", "tagName", "scrollLeft", "scrollTop", "offsetParent", "key", "fallback", "attr", "getAttribute", "prepare", "getInlineOption", "customClassNames", "useClassNames", "animatedClassName", "split", "filter", "initClassName", "defaultOffset", "defaultAnchorPlacement", "windowHeight", "innerHeight", "anchor", "inlineAnchorPlacement", "additionalOffset", "Number", "anchorPlacement", "finalEl", "querySelectorAll", "triggerPoint", "getOffset", "offsetHeight", "getPositionIn", "getPositionOut", "elements", "map", "$aosElements", "initialized", "isBrowserNotSupported", "all", "atob", "refresh", "addEventListener", "throttle", "throttle<PERSON><PERSON><PERSON>", "refreshHard", "isDisabled", "disable", "removeAttribute", "optionDisable", "tablet", "settings", "babelHelpers.extends", "disableMutationObserver", "info", "querySelector", "setAttribute", "easing", "duration", "delay", "indexOf", "startEvent", "readyState", "deboun<PERSON><PERSON><PERSON><PERSON>"], "mappings": "0RAUIA,EAAkB,sBAGlBC,EAAM,IAGNC,EAAY,kBAGZC,EAAS,aAGTC,EAAa,qBAGbC,EAAa,aAGbC,EAAY,cAGZC,EAAeC,SAGfC,EAA8B,iBAAVC,GAAsBA,GAAUA,EAAOC,SAAWA,QAAUD,EAGhFE,EAA0B,iBAARC,MAAoBA,MAAQA,KAAKF,SAAWA,QAAUE,KAGxEC,EAAOL,GAAcG,GAAYG,SAAS,cAATA,GAUjCC,EAPcL,OAAOM,UAOQC,SAG7BC,EAAYC,KAAKC,IACjBC,EAAYF,KAAKG,IAkBjBC,EAAM,WACR,OAAOV,EAAKW,KAAKD,OAyDnB,SAASE,EAASC,EAAMC,EAAMC,GAC5B,IAAIC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EAAiB,EACjBC,GAAU,EACVC,GAAS,EACTC,GAAW,EAEf,GAAmB,mBAARZ,EACT,MAAM,IAAIa,UAAUxC,GAUtB,SAASyC,EAAWC,GAClB,IAAIC,EAAOb,EACPc,EAAUb,EAKd,OAHAD,EAAWC,OAAWc,EACtBT,EAAiBM,EACjBT,EAASN,EAAKmB,MAAMF,EAASD,GAqB/B,SAASI,EAAaL,GACpB,IAAIM,EAAoBN,EAAOP,EAM/B,YAAyBU,IAAjBV,GAA+Ba,GAAqBpB,GACzDoB,EAAoB,GAAOV,GANJI,EAAON,GAM8BJ,EAGjE,SAASiB,IACP,IAAIP,EAAOlB,IACX,GAAIuB,EAAaL,GACf,OAAOQ,EAAaR,GAGtBR,EAAUiB,WAAWF,EAzBvB,SAAuBP,GACrB,IAEIT,EAASL,GAFWc,EAAOP,GAI/B,OAAOG,EAAShB,EAAUW,EAAQD,GAHRU,EAAON,IAGkCH,EAoBhCmB,CAAcV,IAGnD,SAASQ,EAAaR,GAKpB,OAJAR,OAAUW,EAINN,GAAYT,EACPW,EAAWC,IAEpBZ,EAAWC,OAAWc,EACfZ,GAeT,SAASoB,IACP,IAAIX,EAAOlB,IACP8B,EAAaP,EAAaL,GAM9B,GAJAZ,EAAWyB,UACXxB,EAAWyB,KACXrB,EAAeO,EAEXY,EAAY,CACd,QAAgBT,IAAZX,EACF,OAvEN,SAAqBQ,GAMnB,OAJAN,EAAiBM,EAEjBR,EAAUiB,WAAWF,EAAcrB,GAE5BS,EAAUI,EAAWC,GAAQT,EAiEzBwB,CAAYtB,GAErB,GAAIG,EAGF,OADAJ,EAAUiB,WAAWF,EAAcrB,GAC5Ba,EAAWN,GAMtB,YAHgBU,IAAZX,IACFA,EAAUiB,WAAWF,EAAcrB,IAE9BK,EAIT,OAxGAL,EAAO8B,EAAS9B,IAAS,EACrB+B,EAAS9B,KACXQ,IAAYR,EAAQQ,QAEpBL,GADAM,EAAS,YAAaT,GACHV,EAAUuC,EAAS7B,EAAQG,UAAY,EAAGJ,GAAQI,EACrEO,EAAW,aAAcV,IAAYA,EAAQU,SAAWA,GAiG1Dc,EAAUO,OAnCV,gBACkBf,IAAZX,GACF2B,aAAa3B,GAEfE,EAAiB,EACjBN,EAAWK,EAAeJ,EAAWG,OAAUW,GA+BjDQ,EAAUS,MA5BV,WACE,YAAmBjB,IAAZX,EAAwBD,EAASiB,EAAa1B,MA4BhD6B,EA0FT,SAASM,EAASI,GAChB,IAAIC,SAAcD,EAClB,QAASA,IAAkB,UAARC,GAA4B,YAARA,GA4EzC,SAASN,EAASK,GAChB,GAAoB,iBAATA,EACT,OAAOA,EAET,GAhCF,SAAkBA,GAChB,MAAuB,iBAATA,GAtBhB,SAAsBA,GACpB,QAASA,GAAyB,iBAATA,EAsBtBE,CAAaF,IAAU/C,EAAekD,KAAKH,IAAU7D,EA8BpDiE,CAASJ,GACX,OAAO9D,EAET,GAAI0D,EAASI,GAAQ,CACnB,IAAIK,EAAgC,mBAAjBL,EAAMM,QAAwBN,EAAMM,UAAYN,EACnEA,EAAQJ,EAASS,GAAUA,EAAQ,GAAMA,EAE3C,GAAoB,iBAATL,EACT,OAAiB,IAAVA,EAAcA,GAASA,EAEhCA,EAAQA,EAAMO,QAAQnE,EAAQ,IAC9B,IAAIoE,EAAWlE,EAAWmE,KAAKT,GAC/B,OAAQQ,GAAYjE,EAAUkE,KAAKT,GAC/BxD,EAAawD,EAAMU,MAAM,GAAIF,EAAW,EAAI,GAC3CnE,EAAWoE,KAAKT,GAAS9D,GAAO8D,EAGvC,MA9IA,SAAkBpC,EAAMC,EAAMC,GAC5B,IAAIQ,GAAU,EACVE,GAAW,EAEf,GAAmB,mBAARZ,EACT,MAAM,IAAIa,UAAUxC,GAMtB,OAJI2D,EAAS9B,KACXQ,EAAU,YAAaR,IAAYA,EAAQQ,QAAUA,EACrDE,EAAW,aAAcV,IAAYA,EAAQU,SAAWA,GAEnDb,EAASC,EAAMC,GACpBS,QAAWA,EACXL,QAAWJ,EACXW,SAAYA,KC5SZvC,EAAkB,sBAGlBC,EAAM,IAGNC,EAAY,kBAGZC,EAAS,aAGTC,EAAa,qBAGbC,EAAa,aAGbC,EAAY,cAGZC,EAAeC,SAGfC,EAA8B,iBAAVC,GAAsBA,GAAUA,EAAOC,SAAWA,QAAUD,EAGhFE,EAA0B,iBAARC,MAAoBA,MAAQA,KAAKF,SAAWA,QAAUE,KAGxEC,EAAOL,GAAcG,GAAYG,SAAS,cAATA,GAUjCC,EAPcL,OAAOM,UAOQC,SAG7BC,EAAYC,KAAKC,IACjBC,EAAYF,KAAKG,IAkBjBC,EAAM,WACR,OAAOV,EAAKW,KAAKD,OA4MnB,SAASmC,EAASI,GAChB,IAAIC,SAAcD,EAClB,QAASA,IAAkB,UAARC,GAA4B,YAARA,GA4EzC,SAASN,EAASK,GAChB,GAAoB,iBAATA,EACT,OAAOA,EAET,GAhCF,SAAkBA,GAChB,MAAuB,iBAATA,GAtBhB,SAAsBA,GACpB,QAASA,GAAyB,iBAATA,EAsBtBE,CAAaF,IAAU/C,EAAekD,KAAKH,IAAU7D,EA8BpDiE,CAASJ,GACX,OAAO9D,EAET,GAAI0D,EAASI,GAAQ,CACnB,IAAIK,EAAgC,mBAAjBL,EAAMM,QAAwBN,EAAMM,UAAYN,EACnEA,EAAQJ,EAASS,GAAUA,EAAQ,GAAMA,EAE3C,GAAoB,iBAATL,EACT,OAAiB,IAAVA,EAAcA,GAASA,EAEhCA,EAAQA,EAAMO,QAAQnE,EAAQ,IAC9B,IAAIoE,EAAWlE,EAAWmE,KAAKT,GAC/B,OAAQQ,GAAYjE,EAAUkE,KAAKT,GAC/BxD,EAAawD,EAAMU,MAAM,GAAIF,EAAW,EAAI,GAC3CnE,EAAWoE,KAAKT,GAAS9D,GAAO8D,EAGvC,MAtPA,SAAkBpC,EAAMC,EAAMC,GAC5B,IAAIC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EAAiB,EACjBC,GAAU,EACVC,GAAS,EACTC,GAAW,EAEf,GAAmB,mBAARZ,EACT,MAAM,IAAIa,UAAUxC,GAUtB,SAASyC,EAAWC,GAClB,IAAIC,EAAOb,EACPc,EAAUb,EAKd,OAHAD,EAAWC,OAAWc,EACtBT,EAAiBM,EACjBT,EAASN,EAAKmB,MAAMF,EAASD,GAqB/B,SAASI,EAAaL,GACpB,IAAIM,EAAoBN,EAAOP,EAM/B,YAAyBU,IAAjBV,GAA+Ba,GAAqBpB,GACzDoB,EAAoB,GAAOV,GANJI,EAAON,GAM8BJ,EAGjE,SAASiB,IACP,IAAIP,EAAOlB,IACX,GAAIuB,EAAaL,GACf,OAAOQ,EAAaR,GAGtBR,EAAUiB,WAAWF,EAzBvB,SAAuBP,GACrB,IAEIT,EAASL,GAFWc,EAAOP,GAI/B,OAAOG,EAAShB,EAAUW,EAAQD,GAHRU,EAAON,IAGkCH,EAoBhCmB,CAAcV,IAGnD,SAASQ,EAAaR,GAKpB,OAJAR,OAAUW,EAINN,GAAYT,EACPW,EAAWC,IAEpBZ,EAAWC,OAAWc,EACfZ,GAeT,SAASoB,IACP,IAAIX,EAAOlB,IACP8B,EAAaP,EAAaL,GAM9B,GAJAZ,EAAWyB,UACXxB,EAAWyB,KACXrB,EAAeO,EAEXY,EAAY,CACd,QAAgBT,IAAZX,EACF,OAvEN,SAAqBQ,GAMnB,OAJAN,EAAiBM,EAEjBR,EAAUiB,WAAWF,EAAcrB,GAE5BS,EAAUI,EAAWC,GAAQT,EAiEzBwB,CAAYtB,GAErB,GAAIG,EAGF,OADAJ,EAAUiB,WAAWF,EAAcrB,GAC5Ba,EAAWN,GAMtB,YAHgBU,IAAZX,IACFA,EAAUiB,WAAWF,EAAcrB,IAE9BK,EAIT,OAxGAL,EAAO8B,EAAS9B,IAAS,EACrB+B,EAAS9B,KACXQ,IAAYR,EAAQQ,QAEpBL,GADAM,EAAS,YAAaT,GACHV,EAAUuC,EAAS7B,EAAQG,UAAY,EAAGJ,GAAQI,EACrEO,EAAW,aAAcV,IAAYA,EAAQU,SAAWA,GAiG1Dc,EAAUO,OAnCV,gBACkBf,IAAZX,GACF2B,aAAa3B,GAEfE,EAAiB,EACjBN,EAAWK,EAAeJ,EAAWG,OAAUW,GA+BjDQ,EAAUS,MA5BV,WACE,YAAmBjB,IAAZX,EAAwBD,EAASiB,EAAa1B,MA4BhD6B,GCzPLqB,EAAW,aAsBf,SAASC,EAAMC,GACRA,KAEKC,QAAQ,gBACVC,EAAaC,MAAM9D,UAAUwD,MAAMP,KAAKc,EAASF,YACjDG,EAAeF,MAAM9D,UAAUwD,MAAMP,KAAKc,EAASC,iBAzB7D,SAASC,EAAgBC,OACnBC,SAAGC,aAEFD,EAAI,EAAGA,EAAID,EAAMG,OAAQF,GAAK,EAAG,OACtBD,EAAMC,IAEJG,SAAWF,EAAYE,QAAQC,WACtC,KAGAH,EAAYI,UAAYP,EAAgBG,EAAYI,iBAGpD,SAIJ,EAWDP,CAFaJ,EAAWY,OAAOT,WAG1BP,MAKb,SAASiB,WAELC,OAAOC,kBACPD,OAAOE,wBACPF,OAAOG,2BAsBMC,YAlBjB,mBACWL,KAiBmBM,MAd9B,SAAeC,EAAUC,OACjBC,EAAMR,OAAOS,SAGbC,EAAW,IAFQX,IAER,CAAqBhB,KAC3BwB,IAEFI,QAAQH,EAAII,4BACR,WACF,gBACK,8fCtDZC,EAAa,2TACbC,EAAW,0kDACXC,EAAmB,sVACnBC,EAAiB,0kDAEvB,SAASC,WACAC,UAAUC,WAAaD,UAAUE,QAAUpB,OAAOqB,OAAS,SA2BrD,oFAtBLC,EAAIL,aACAJ,EAAWjC,KAAK0C,KAAMR,EAASlC,KAAK0C,EAAEC,OAAO,EAAG,0CAIpDD,EAAIL,aACAF,EAAiBnC,KAAK0C,KAAMN,EAAepC,KAAK0C,EAAEC,OAAO,EAAG,6CAI/D3D,KAAK4D,WAAa5D,KAAK6D,6CAM5B,qBAAsBhB,SAASG,gBAAgBc,OAC/C,kBAAmBjB,SAASG,gBAAgBc,gBCd5CC,EAAY,SAACC,EAAWC,OACxBC,gBAEAC,EAAOC,UACKvB,SAASwB,YAAY,gBACvBC,gBAAgBN,GAAW,GAAM,GAAQO,OAAQN,MAE/C,IAAIO,YAAYR,UACpBC,IAILpB,SAAS4B,cAAcP,IAoD1BQ,EAAe,mBACnBC,EAAUtD,QAAQ,SAACuD,EAAIhD,UA7CJ,SAACgD,EAAIC,OAChBxG,EAAkCuG,EAAlCvG,QAASyG,EAAyBF,EAAzBE,SAAUC,EAAeH,EAAfG,KAErBC,GAFoCJ,EAATX,KAEpB,WACNW,EAAGK,WA3BU,SAACF,EAAMG,GAC3BA,GAAWA,EAAQ7D,QAAQ,mBAAa0D,EAAKI,UAAUC,OAAOC,MA4B9CN,EAAM1G,EAAQiH,sBAClB,UAAWP,GAEjBH,EAAGvG,QAAQkH,gBACOX,EAAGvG,QAAQkH,GAAMR,KAGpCE,UAAW,KAgBZ5G,EAAQmH,QAAUX,GAAOC,EAASW,MAAQpH,EAAQqH,SAE3Cb,GAAOC,EAASa,GAdrBf,EAAGK,WAhDQ,SAACF,EAAMG,GACxBA,GAAWA,EAAQ7D,QAAQ,mBAAa0D,EAAKI,UAAUS,IAAIP,MAiD9CN,EAAM1G,EAAQiH,sBAEf,SAAUP,GAChBH,EAAGvG,QAAQkH,gBACOX,EAAGvG,QAAQkH,GAAMR,KAGpCE,UAAW,GAOLL,EAAGK,WAAa5G,EAAQqH,UAYNG,CAAajB,EAAIxC,OAAO0D,gBC5EjDC,EAAS,SAASnB,WAClBoB,EAAK,EACLC,EAAK,EAEFrB,IAAOsB,MAAMtB,EAAGuB,cAAgBD,MAAMtB,EAAGwB,eACxCxB,EAAGuB,YAA4B,QAAdvB,EAAGyB,QAAoBzB,EAAG0B,WAAa,MACxD1B,EAAGwB,WAA2B,QAAdxB,EAAGyB,QAAoBzB,EAAG2B,UAAY,KACvD3B,EAAG4B,wBAIHP,OACCD,gBCVMpB,EAAI6B,EAAKC,OACjBC,EAAO/B,EAAGgC,aAAa,YAAcH,WAEvB,IAATE,EAAsB,IAClB,SAATA,SACK,EACF,GAAa,UAATA,SACF,SAIJA,GAAQD,GCfXG,GAAU,SAASlC,EAAWtG,YACxBgD,QAAQ,SAACuD,EAAIhD,OACf4D,EAASsB,GAAgBlC,EAAGG,KAAM,SAAU1G,EAAQmH,QACpDE,EAAOoB,GAAgBlC,EAAGG,KAAM,OAAQ1G,EAAQqH,MAChDH,EAAKuB,GAAgBlC,EAAGG,KAAM,MAC9BgC,EACJ1I,EAAQ2I,eAAiBpC,EAAGG,KAAK6B,aAAa,YAE1CtB,GAAsBjH,EAAQ4I,mBACjC/E,OAAO6E,EAAmBA,EAAiBG,MAAM,SACjDC,OAAO,kBAAkC,iBAAd9B,IAE1BhH,EAAQ+I,iBACPrC,KAAKI,UAAUS,IAAIvH,EAAQ+I,iBAG7BtC,aCRsB,SAACF,EAAIyC,EAAeC,OACzCC,EAAenF,OAAOoF,YACtBC,EAASX,GAAgBlC,EAAI,UAC7B8C,EAAwBZ,GAAgBlC,EAAI,oBAC5C+C,EAAmBC,OACvBd,GAAgBlC,EAAI,SAAU8C,EAAwB,EAAIL,IAEtDQ,EAAkBH,GAAyBJ,EAC7CQ,EAAUlD,EAEV6C,GAAU5E,SAASkF,iBAAiBN,OAC5B5E,SAASkF,iBAAiBN,GAAQ,QAG1CO,EAAeC,EAAUH,GAASjD,IAAM0C,SAEpCM,OACD,uBAGA,mBACaC,EAAQI,aAAe,YAEpC,mBACaJ,EAAQI,uBAErB,gBACaX,EAAe,YAE5B,mBACaA,EAAe,EAAIO,EAAQI,aAAe,YAEvD,mBACaX,EAAe,EAAIO,EAAQI,uBAExC,aACaX,YAEb,gBACaA,EAAeO,EAAQI,uBAEpC,gBACaX,EAAeO,EAAQI,aAAe,SAInDF,EAAeL,EDrCdQ,CAAcvD,EAAGG,KAAM1G,EAAQ0H,OAAQ1H,EAAQwJ,qBAC9CrC,GCuCmB,SAACZ,EAAIyC,GACZjF,OAAOoF,gBACtBC,EAASX,GAAgBlC,EAAI,UAC7B+C,EAAmBb,GAAgBlC,EAAI,SAAUyC,GACnDS,EAAUlD,SAEV6C,GAAU5E,SAASkF,iBAAiBN,OAC5B5E,SAASkF,iBAAiBN,GAAQ,IAGrBQ,EAAUH,GAASjD,IAElBiD,EAAQI,aAAeP,EDnD9BS,CAAexD,EAAGG,KAAM1G,EAAQ0H,WAG9C1H,sDAQEsG,qBE5BD0D,EAAWxF,SAASkF,iBAAiB,qBACpCxG,MAAM9D,UAAU6K,IAAI5H,KAAK2H,EAAU,mBAAWtD,WCenDwD,MACAC,IAAc,EAKdnK,WACM,UACD,SACC,gBACE,aACD,QACH,UACE,kBACS,wBACL,qCACO,4BACJ,0BACA,2BACU,gBACV,iBACA,IAKXoK,GAAwB,kBAAM5F,SAAS6F,MAAQtG,OAAOuG,MAwBtDC,GAAU,qEAEEJ,IAAc,GAC1BA,QAvBW3B,GAAQ0B,GAAclK,MAExBkK,WAKNM,iBACL,SACAC,EAAS,aACMP,GAAclK,GAAQqH,OAClCrH,GAAQ0K,kBAmBTC,GAAc,iBACHX,KAEXY,GAAW5K,GAAQ6K,UAAYT,YAC1BS,WAULA,GAAU,cACD7H,QAAQ,SAASuD,EAAIhD,KAC7BmD,KAAKoE,gBAAgB,cACrBpE,KAAKoE,gBAAgB,qBACrBpE,KAAKoE,gBAAgB,uBACrBpE,KAAKoE,gBAAgB,kBAEpB9K,GAAQ+I,iBACPrC,KAAKI,UAAUC,OAAO/G,GAAQ+I,eAG/B/I,GAAQ4I,qBACPlC,KAAKI,UAAUC,OAAO/G,GAAQ4I,sBAQjCgC,GAAa,SAASG,UAEN,IAAlBA,GACmB,WAAlBA,GAA8BjF,EAAOP,UACnB,UAAlBwF,GAA6BjF,EAAON,SAClB,WAAlBuF,GAA8BjF,EAAOkF,UACZ,mBAAlBD,IAAoD,IAApBA,iBAa/B,SAAcE,aACfC,EAAclL,GAASiL,MAGlBjB,KAKVhK,GAAQmL,yBAA4B1G,EAASN,wBACxCiH,6LAKAD,yBAA0B,GAQ/BnL,GAAQmL,2BACF/G,MAAM,aAAcuG,IAO3BC,GAAW5K,GAAQ6K,UAAYT,KAC1BS,eAQNQ,cAAc,QACdC,aAAa,kBAAmBtL,GAAQuL,iBAGxCF,cAAc,QACdC,aAAa,oBAAqBtL,GAAQwL,mBAEpCH,cAAc,QAAQC,aAAa,iBAAkBtL,GAAQyL,QAKJ,KAA7D,mBAAoB,QAAQC,QAAQ1L,GAAQ2L,qBAEtCnB,iBAAiBxK,GAAQ2L,WAAY,eACpC,YAGHnB,iBAAiB,OAAQ,eACtB,KAKa,qBAAvBxK,GAAQ2L,aACP,WAAY,eAAeD,QAAQlH,SAASoH,aAAe,OAGpD,UAMHpB,iBACL,SACA3K,EAAS0K,GAASvK,GAAQ6L,eAAe,WAGpCrB,iBACL,oBACA3K,EAAS0K,GAASvK,GAAQ6L,eAAe,IAGpC3B"}